"-Xallow-no-source-files" "-classpath" "D:\\project\\ai-dance\\legend_dance\\build\\fluwx\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5c86918175c17465b680780028235442\\transformed\\jetified-wechat-sdk-android-6.8.34-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c22940fd007c288ac2172fb605aed0f1\\transformed\\jetified-flutter_embedding_debug-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0c43bb5fcbf5d527acc938eed5c5ee7\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fb1a370ddc9b6f09e36542d47d36296d\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df5d28f461ffd56f36cdd72acdfc344f\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d6f4d647e17b29c5423a0c7fa8c98f2d\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ae7f7f186029d473ac3dbfcddcd85b22\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\115b656e1a4aecf880260d6d83623996\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c315d47e2f7879c3deeb8a2382af17bc\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\890fa23f232b0e27acd86381ae22e986\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\496b2ffad9f48ea28eeff97372eecba8\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0718b469eda36dc9592c0463fc44f0f5\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aaa838aa6779c7b3265d9262098da713\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\15ba965be5d36f1409c507c4f4f01060\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8072e09447510b0de763fa662fba307a\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd0b64dbcc747184e3367538d3940e0\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\330e024619e0214797258b4ad0dce20a\\transformed\\jetified-compressor-3.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fff59de3233a53a251731adb4c6ced0a\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ca08bea8da6ce44b915b59db148eb4a9\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c88fd7353bfda1fa24683dd690bfd1f1\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0edd7cf7d00e51d9b4e98970c281f4f0\\transformed\\jetified-kotlinx-coroutines-android-1.9.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ac11ff659a06e48009b7e37d2ac532c0\\transformed\\jetified-okhttp-4.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d989754dc24007f960e6add0f653abc8\\transformed\\jetified-okio-jvm-3.6.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\558bb02007e4b04e9700d76f5158828e\\transformed\\jetified-kotlin-stdlib-jdk8-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b9083ee1762804f310e9e662482f5139\\transformed\\jetified-kotlin-stdlib-jdk7-1.9.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2d84b4d753bef46c4f0c33072ed597d9\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\546c29fc3b12ba0ada2deef99fc20985\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f1860f1dedc07b8c1db1817385fde25\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\82f50a1147ea7962ad05f59f739641fb\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4763d8b74513eee13e47c55bba2325f2\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ade16677ec891dea4619d807ecb70bab\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1a24e07e8946987328cdbf73d5c0d1c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cd60595a499747cfa90d326929eabb59\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e2cbe243dd93764f3ea77e6c23ccd98f\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e9b80c786d1f3bf9007353926f4b6eb1\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\project\\ai-dance\\legend_dance\\build\\fluwx\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "fluwx_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\utils\\WXApiUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\FluwxFileProvider.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\FluwxPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\handlers\\FluwxAuthHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\handlers\\FluwxRequestHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\handlers\\FluwxShareHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\handlers\\PermissionHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\handlers\\WXAPiHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\io\\ByteArrayToFile.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\io\\ImagesIO.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\io\\WeChatFiles.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\utils\\FluwxExtensions.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\fluwx-5.7.2\\android\\src\\main\\kotlin\\com\\jarvan\\fluwx\\wxapi\\FluwxWXEntryActivity.kt" "D:\\project\\ai-dance\\legend_dance\\build\\fluwx\\generated\\src\\kotlin\\com\\jarvan\\fluwx\\FluwxConfigurations.kt"